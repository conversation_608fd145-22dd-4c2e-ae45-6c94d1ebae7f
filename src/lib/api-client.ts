import { API_CONFIG } from './api-config';
import { TypedApiClient } from './generated-api-client';
import type { components } from '@/types/api';
import type { Workspace as AppWorkspace, UserProfile as AppUserProfile, AuthSession as AppAuthSession, SignUpData, CompanyType } from '@/types';

// Session response type for the /get-session endpoint
export interface SessionResponse {
  session: {
    id: string;
    userId: string;
    activeOrganizationId?: string | null;
    expiresAt: string;
    createdAt: string;
    updatedAt: string;
  };
  user: {
    id: string;
    email: string;
    name?: string;
    emailVerified: boolean;
    image?: string;
    createdAt: string;
    updatedAt: string;
  };
}

// App-level types (from our domain models)
export interface User {
  id: string;
  email?: string;
  [key: string]: unknown;
}
export type Workspace = AppWorkspace;
export type UserProfile = AppUserProfile;
export type AuthSession = AppAuthSession;

// Profile types (from API schemas)
export type UpdateProfileRequest = components['schemas']['UpdateProfileRequest'];
export type UpdateProfileResponse = components['schemas']['UpdateProfileResponse'];

// File types
export type UploadedFile = components['schemas']['File'];
export type UploadFileRequest = components['schemas']['UploadFileRequest'];
export type UploadFileResponse = components['schemas']['UploadFileResponse'];
export type GetFileResponse = components['schemas']['GetFileResponse'];
export type DeleteResponse = components['schemas']['DeleteResponse'];

// Listing types
export type ListingResponse = components['schemas']['ListingResponse'];
export type ListingListResponse = components['schemas']['ListingListResponse'];
export type SingleListingResponse = components['schemas']['SingleListingResponse'];
export type CreateListingRequest = components['schemas']['CreateListingRequest'];
export type UpdateListingRequest = components['schemas']['UpdateListingRequest'];
export type SaveDraftListingRequest = components['schemas']['SaveDraftListingRequest'];
export type BulkCreateResponse = components['schemas']['BulkCreateResponse'];

// Workspace types (mapped to app domain models or lightweight placeholders)
export type SingleWorkspaceResponse = Workspace;
export type WorkspaceUpdateResponse = Workspace;
export type UpdateWorkspaceRequest = Partial<Workspace>;
export type WorkspaceInvitation = import('@/types').WorkspaceInvitation;
export type WorkspaceInvitationListResponse = { data: WorkspaceInvitation[] };
export type SingleWorkspaceInvitationResponse = { data: WorkspaceInvitation };
export type CreateWorkspaceInvitationRequest = { email: string; role: string; redirectTo?: string };
export type UpdateWorkspaceInvitationRequest = { role?: string; status?: string };

// Convenient type alias
export type Listing = ListingResponse;

// Legacy types for backward compatibility (remove once all usage is updated)
export interface UserWorkspacesResponse {
  workspaces: {
    workspace_id: string;
    workspace_name: string;
    company_name: string;
    user_role: string;
    is_active: boolean;
    status: string;
    subscription_plan: string;
    logo_url?: string;
    primary_color?: string;
  }[];
}

export interface SwitchWorkspaceRequest {
  workspace_id: string;
}

export interface SwitchWorkspaceResponse {
  success: boolean;
  workspace: {
    id: string;
    companyName: string;
    companyType: string;
    subscriptionPlan: string;
    status: string;
    createdAt: string;
  };
  profile: {
    id: string;
    workspaceId: string;
    role: string;
    firstName: string;
    lastName: string;
    displayName: string;
    email: string;
  };
  session: {
    access_token: string;
    refresh_token: string;
    expires_at: number;
  };
}

export interface ApiError {
  message: string;
  status?: number;
  code?: string;
}

// Type-safe API client that wraps the generated client
class ApiClient {
  private client: TypedApiClient;

  constructor() {
    this.client = new TypedApiClient({
      baseUrl: API_CONFIG.BASE_URL,
      timeout: API_CONFIG.TIMEOUT,
    });
  }

  // Helper to extract data from API response
  private extractData<T>(response: { data: T }): T {
    return response.data;
  }

  // Helper to handle API errors
  private async handleRequest<T>(request: Promise<{ data: T }>): Promise<T> {
    try {
      const response = await request;
      return this.extractData(response);
    } catch (error) {
      // Convert the error to our ApiError format
      if (error instanceof Error) {
        const apiError: ApiError = {
          message: error.message,
        };
        
        // Extract status code if available in the error message
        const statusMatch = error.message.match(/HTTP (\d+):/);
        if (statusMatch) {
          apiError.status = parseInt(statusMatch[1], 10);
        }
        
        throw apiError;
      }
      throw error;
    }
  }

  // Authentication methods

  // Get current session - uses the /get-session endpoint with cookie authentication
  async getSession(): Promise<SessionResponse> {
    const response = await fetch(`${API_CONFIG.BASE_URL}/get-session`, {
      method: 'GET',
      credentials: 'include',
    });

    if (!response.ok) {
      const message = await response.text();
      const error: any = new Error(message || `HTTP ${response.status}`);
      error.status = response.status;
      throw error;
    }

    return (await response.json()) as SessionResponse;
  }

  async signOut(data: Record<string, unknown> = {}): Promise<{ success: boolean; message?: string }>
  {
    return this.handleRequest(this.client.signOut(data as any));
  }

  async refreshToken(data: Record<string, unknown> = {}): Promise<{ success: boolean; message?: string }>
  {
    return this.handleRequest(this.client.refreshToken(data as any));
  }
  
  async forgotPassword(data: { email: string }): Promise<{ success: boolean; message: string }>
  {
    return this.handleRequest(this.client.forgotPassword(data));
  }

  async resetPassword(data: { token: string; password: string }): Promise<{ success: boolean; message?: string }>
  {
    return this.handleRequest(this.client.resetPassword(data));
  }

  // New custom email/password auth endpoints
  async signInWithEmail(data: { email: string; password: string; rememberMe?: boolean }): Promise<components['schemas']['AuthResponse']>
  {
    const body: components['schemas']['SignInRequest'] = {
      email: data.email,
      password: data.password,
      callbackURL: undefined,
      rememberMe: data.rememberMe ?? true,
    } as components['schemas']['SignInRequest'];

    // Use raw client to hit the new custom endpoint
    const response = await this.client.post<components['schemas']['AuthResponse']>(
      '/v1/auth/sign-in/email',
      body
    );
    return this.extractData(response);
  }

  async signUpWithEmail(data: SignUpData): Promise<components['schemas']['AuthResponse']>
  {
    // Map app form fields to API schema
    const payload: components['schemas']['SignUpRequest'] = {
      first_name: data.first_name,
      last_name: data.last_name,
      email: data.email,
      company_name: data.company_name,
      company_type: (data.company_type as unknown as CompanyType),
      password: data.password,
      confirm_password: data.confirmPassword,
      terms_accepted: data.terms_accepted,
      phone: data.phone,
      license_number: data.license_number,
      website: (data.website ?? '') as any,
      business_address: data.address,
      marketing_consent: data.marketing_consent ?? false,
      image: undefined,
      callbackURL: undefined,
      rememberMe: true,
    } as components['schemas']['SignUpRequest'];

    const response = await this.client.post<components['schemas']['AuthResponse']>(
      '/v1/auth/sign-up/email',
      payload
    );
    return this.extractData(response);
  }

  // User Profile methods
  async getUserProfile(): Promise<UserProfile> {
    return this.handleRequest(this.client.getUserProfile());
  }

  async updateUserProfile(
    data: UpdateProfileRequest
  ): Promise<UpdateProfileResponse> {
    return this.handleRequest(this.client.updateUserProfile(data));
  }

  async verifyEmail(data: { token: string }): Promise<{ success: boolean; message?: string }> {
    return this.handleRequest(this.client.verifyEmail(data));
  }

  // Workspace methods
  async getCurrentWorkspace(): Promise<Workspace> {
    const response = await this.handleRequest(this.client.getCurrentWorkspace());
    return response.data;
  }

  async updateCurrentWorkspace(
    data: UpdateWorkspaceRequest
  ): Promise<WorkspaceUpdateResponse> {
    return this.handleRequest(this.client.updateCurrentWorkspace(data));
  }

  // Workspace Invitation methods
  async getWorkspaceInvitations(
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      role?: "owner" | "admin" | "member" | "viewer";
    }
  ): Promise<WorkspaceInvitationListResponse> {
    const queryParams: Record<string, string | number> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams[key] = String(value);
        }
      });
    }

    return this.handleRequest(this.client.getWorkspaceInvitations(queryParams));
  }

  async createWorkspaceInvitation(
    data: CreateWorkspaceInvitationRequest,
  ): Promise<WorkspaceInvitation> {
    const response = await this.handleRequest(this.client.createWorkspaceInvitation(data));
    return response.data;
  }

  async updateWorkspaceInvitation(
    invitationId: string,
    data: UpdateWorkspaceInvitationRequest
  ): Promise<WorkspaceInvitation> {
    const response = await this.handleRequest(this.client.updateWorkspaceInvitation(invitationId, data));
    return response.data;
  }

  async deleteWorkspaceInvitation(
    invitationId: string
  ): Promise<DeleteResponse> {
    return this.handleRequest(this.client.deleteWorkspaceInvitation(invitationId));
  }

  async resendWorkspaceInvitation(
    invitationId: string
  ): Promise<WorkspaceInvitation> {
    const response = await this.handleRequest(this.client.resendWorkspaceInvitation(invitationId));
    return response.data;
  }

  // Legacy workspace methods (for backward compatibility)
  async getUserWorkspaces(): Promise<UserWorkspacesResponse> {
    // This might need to be implemented differently based on actual API structure
    const response = await this.client.get('/profile');
    return this.extractData(response) as UserWorkspacesResponse;
  }

  async switchWorkspace(
    data: SwitchWorkspaceRequest
  ): Promise<SwitchWorkspaceResponse> {
    // This endpoint might need to be custom implemented
    const response = await this.client.post('/workspaces/switch', data);
    return this.extractData(response) as SwitchWorkspaceResponse;
  }

  // File Upload methods
  async uploadFile(
    file: Blob | File,
    options: {
      fileType: 'document' | 'image' | 'video' | 'audio' | 'other';
      entityType?: string;
      entityId?: string;
      isPublic?: boolean;
    }
  ): Promise<UploadFileResponse> {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('fileType', options.fileType);

    if (options.entityType) {
      formData.append('entityType', options.entityType);
    }
    if (options.entityId) {
      formData.append('entityId', options.entityId);
    }
    if (options.isPublic !== undefined) {
      formData.append('isPublic', String(options.isPublic));
    }

    return this.handleRequest(this.client.uploadFile(formData));
  }

  async getFile(fileId: string): Promise<GetFileResponse> {
    return this.handleRequest(this.client.getFile(fileId));
  }

  async deleteFile(fileId: string): Promise<DeleteResponse> {
    return this.handleRequest(this.client.deleteFile(fileId));
  }

  // Listing methods
  async getListings(
    params?: {
      page?: number;
      limit?: number;
      status?: string;
      industry?: string;
      assignedTo?: string;
      minPrice?: number;
      maxPrice?: number;
      location?: string;
      sortBy?: 'created_at' | 'updated_at' | 'asking_price' | 'business_name' | 'date_listed' | 'days_listed';
      sortOrder?: 'asc' | 'desc';
      search?: string;
    }
  ): Promise<ListingListResponse> {
    // Convert params to the format expected by the generated client
    const queryParams: Record<string, string | number> = {};
    if (params) {
      Object.entries(params).forEach(([key, value]) => {
        if (value !== undefined && value !== null) {
          queryParams[key] = String(value);
        }
      });
    }

    return this.handleRequest(this.client.getListings(queryParams));
  }



  async getListing(listingId: string, includeDetails: boolean = true): Promise<ListingResponse> {
    const params = includeDetails ? { includeDetails: 'true' } : {};
    const response = await this.handleRequest(this.client.getListing(listingId, params));
    return response.data;
  }

  async createListing(listing: CreateListingRequest): Promise<ListingResponse> {
    console.log('API Client: Creating listing with cookie authentication');

    try {
      const response = await this.handleRequest(this.client.createListing(listing));
      console.log('API Client: Listing created successfully with cookie authentication');
      return response.data;
    } catch (error) {
      console.error('API Client: Cookie authentication listing creation failed:', error);
      throw error;
    }
  }

  async updateListing(listingId: string, listing: UpdateListingRequest): Promise<ListingResponse> {
    const response = await this.handleRequest(this.client.updateListing(listingId, listing));
    return response.data;
  }



  async deleteListing(listingId: string): Promise<DeleteResponse> {
    return this.handleRequest(this.client.deleteListing(listingId));
  }

  async saveDraftListing(listing: SaveDraftListingRequest): Promise<ListingResponse> {
    const response = await this.handleRequest(this.client.saveDraftListing(listing));
    return response.data;
  }



  // CSV import for listings
  async importListingsFromCSV(file: File): Promise<BulkCreateResponse> {
    const formData = new FormData();
    formData.append('file', file);

    return this.handleRequest(this.client.uploadListingsCsv(formData));
  }

  // Legacy methods (for backward compatibility)
  async updateListingStatus(
    listingId: string,
    statusUpdate: any
  ): Promise<any> {
    // This endpoint might need to be added to the generated client
    const response = await this.client.patch(`/v1/listings/${listingId}/status`, statusUpdate);
    return this.extractData(response);
  }

  async getListingStatusHistory(listingId: string): Promise<any> {
    // This endpoint might need to be added to the generated client
    const response = await this.client.get(`/v1/listings/${listingId}/status-history`);
    return this.extractData(response);
  }

  async createBulkListings(listings: any): Promise<BulkCreateResponse> {
    const response = await this.client.post('/v1/listings/bulk/csv', listings);
    return this.extractData(response) as BulkCreateResponse;
  }

  // Utility method to make authenticated requests (for endpoints not in generated client)
  async makeAuthenticatedRequest<T>(
    url: string,
    options: RequestInit = {}
  ): Promise<T> {
    const method = (options.method || 'GET').toLowerCase();
    const body = options.body ? JSON.parse(options.body as string) : undefined;

    let response: any;
    switch (method) {
      case 'get':
        response = await this.client.get(url);
        break;
      case 'post':
        response = await this.client.post(url, body);
        break;
      case 'put':
        response = await this.client.put(url, body);
        break;
      case 'delete':
        response = await this.client.delete(url);
        break;
      case 'patch':
        response = await this.client.patch(url, body);
        break;
      default:
        throw new Error(`Unsupported HTTP method: ${method}`);
    }

    return this.extractData(response) as T;
  }
}

// Export singleton instance
export const apiClient = new ApiClient();

// Helper function to check if error is an API error
export const isApiError = (error: unknown): error is ApiError => {
  return (
    typeof error === 'object' &&
    error !== null &&
    'message' in error &&
    typeof (error as any).message === 'string'
  );
}; 