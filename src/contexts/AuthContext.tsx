import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { User } from '@/lib/api-client';
import { apiClient, isApiError } from '@/lib/api-client';
import {
  useSessionQuery,
  useSignInMutation,
  useSignUpMutation,
  useSignOutMutation,
} from '@/hooks/useAuthApi';
import {
  AuthContextType,
  UserProfile,
  Workspace,
  SignUpData,
  AuthError,
  AuthErrorCode
} from '@/types';


const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // Use React Query for session management
  const {
    data: sessionData,
    isLoading: sessionLoading,
    error: sessionError,
    refetch: refetchSession,
  } = useSessionQuery();

  // Extract user and session from the session data
  const user = sessionData?.user || null;
  const session = sessionData?.session || null;

  // Local state for profile and workspace (these might come from separate endpoints)
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [error, setError] = useState<AuthError | null>(null);

  // Auth mutations
  const signInMutation = useSignInMutation();
  const signUpMutation = useSignUpMutation();
  const signOutMutation = useSignOutMutation();

  // Helper function to create AuthError from various error types
  const createAuthError = useCallback((error: any, code?: AuthErrorCode): AuthError => {
    let errorCode = code || AuthErrorCode.UNKNOWN_ERROR;
    let message = 'An unknown error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Map common API error messages to our error codes
    if (message.includes('Invalid login credentials') || message.includes('Invalid email or password')) {
      errorCode = AuthErrorCode.INVALID_CREDENTIALS;
    } else if (message.includes('Email not confirmed') || message.includes('Email not verified')) {
      errorCode = AuthErrorCode.EMAIL_NOT_VERIFIED;
    } else if (message.includes('Password should be at least') || message.includes('weak password')) {
      errorCode = AuthErrorCode.WEAK_PASSWORD;
    } else if (message.includes('User already registered') || message.includes('Email already exists')) {
      errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
    } else if (message.includes('Signups not allowed')) {
      errorCode = AuthErrorCode.SIGNUP_DISABLED;
    } else if (message.includes('network') || message.includes('fetch')) {
      errorCode = AuthErrorCode.NETWORK_ERROR;
    }

    return {
      code: errorCode,
      message,
      timestamp: new Date().toISOString(),
    };
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Clear all auth data from memory
  const clearAuthData = useCallback(() => {
    setProfile(null);
    setWorkspace(null);
  }, []);

  // For now, we'll handle workspace data separately since we removed better-auth organization support
  // This can be implemented later with a dedicated workspace API
  const hydrateWorkspaceFromSession = useCallback(async (activeOrganizationId?: string | null) => {
    try {
      // TODO: Implement workspace fetching from your API
      // For now, we'll set a default workspace or fetch from a different endpoint
      setWorkspace(null);
    } catch (e) {
      setWorkspace(null);
    }
  }, []);

  // Sign in function using React Query mutation
  const signIn = useCallback(async (email: string, password: string) => {
    try {
      setError(null);
      console.log('AuthContext: Signing in with React Query...');

      await signInMutation.mutateAsync({ email, password, rememberMe: true });

      // Refetch session to get updated user data
      await refetchSession();

      console.log('AuthContext: Sign in successful');
    } catch (error) {
      console.error('AuthContext: Sign in failed:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    }
  }, [signInMutation, refetchSession, createAuthError]);

  // Sign up function using React Query mutation
  const signUp = useCallback(async (data: SignUpData) => {
    try {
      setError(null);
      console.log('AuthContext: Signing up with React Query...');

      await signUpMutation.mutateAsync(data);

      // Refetch session to get updated user data
      await refetchSession();

      console.log('AuthContext: Sign up successful');
    } catch (error) {
      console.error('AuthContext: Sign up failed:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 409) {
          errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
        } else if (error.status === 400) {
          errorCode = AuthErrorCode.WEAK_PASSWORD;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    }
  }, [signUpMutation, refetchSession, createAuthError]);




  // Sign out function using React Query mutation
  const signOut = useCallback(async () => {
    try {
      setError(null);
      console.log('AuthContext: Signing out with React Query...');

      await signOutMutation.mutateAsync({});

      // Clear local state
      clearAuthData();

      console.log('AuthContext: Sign out successful');
    } catch (error) {
      console.error('AuthContext: Sign out failed:', error);

      // Even if sign out fails on the server, clear local state
      clearAuthData();

      setError(createAuthError(error));
    }
  }, [signOutMutation, clearAuthData, createAuthError]);

  // Switch workspace - simplified for now since we removed better-auth organization support
  const switchWorkspace = useCallback(async (workspaceId: string) => {
    try {
      setError(null);
      console.log('AuthContext: Switching workspace...');

      // TODO: Implement workspace switching with your API
      // For now, we'll just update the local workspace state
      setWorkspace(null);

      console.log('AuthContext: Workspace switch successful');
    } catch (error) {
      console.error('AuthContext: Workspace switch failed:', error);
      setError(createAuthError(error));
      throw error;
    }
  }, [createAuthError]);


  // Handle session errors and update local error state
  useEffect(() => {
    if (sessionError) {
      setError(createAuthError(sessionError, AuthErrorCode.SESSION_EXPIRED));
    } else {
      // Clear error when session is successful
      setError(null);
    }
  }, [sessionError, createAuthError]);

  // Determine loading state - combine session loading with mutation loading
  const loading = sessionLoading || signInMutation.isPending || signUpMutation.isPending || signOutMutation.isPending;

  // Note: With cookie-based authentication, session management is handled by the server
  // No need for client-side token refresh intervals

  const value: AuthContextType = {
    user,
    profile,
    workspace,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    switchWorkspace,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};