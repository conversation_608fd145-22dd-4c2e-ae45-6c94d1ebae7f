import React, { createContext, useContext, useEffect, useState, useCallback } from 'react';
import { User } from '@/lib/api-client';
// import { supabase } from '@/lib/supabase';
import { apiClient, isApiError } from '@/lib/api-client';
import { betterAuthClient } from '@/lib/better-auth-client';
import {
  AuthContextType,
  UserProfile,
  Workspace,
  SignUpData,
  AuthError,
  AuthErrorCode
} from '@/types';


const AuthContext = createContext<AuthContextType | undefined>(undefined);

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

interface AuthProviderProps {
  children: React.ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  // All user data stored in memory only
  const [user, setUser] = useState<User | null>(null);
  const [profile, setProfile] = useState<UserProfile | null>(null);
  const [workspace, setWorkspace] = useState<Workspace | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<AuthError | null>(null);

  // Helper function to create AuthError from various error types
  const createAuthError = useCallback((error: any, code?: AuthErrorCode): AuthError => {
    let errorCode = code || AuthErrorCode.UNKNOWN_ERROR;
    let message = 'An unknown error occurred';

    if (error instanceof Error) {
      message = error.message;
    } else if (typeof error === 'string') {
      message = error;
    } else if (error?.message) {
      message = error.message;
    }

    // Map common Supabase error messages to our error codes
    if (message.includes('Invalid login credentials')) {
      errorCode = AuthErrorCode.INVALID_CREDENTIALS;
    } else if (message.includes('Email not confirmed')) {
      errorCode = AuthErrorCode.EMAIL_NOT_VERIFIED;
    } else if (message.includes('Password should be at least')) {
      errorCode = AuthErrorCode.WEAK_PASSWORD;
    } else if (message.includes('User already registered')) {
      errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
    } else if (message.includes('Signups not allowed')) {
      errorCode = AuthErrorCode.SIGNUP_DISABLED;
    } else if (message.includes('network') || message.includes('fetch')) {
      errorCode = AuthErrorCode.NETWORK_ERROR;
    }

    return {
      code: errorCode,
      message,
      timestamp: new Date().toISOString(),
    };
  }, []);

  // Clear error function
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  // Token storage utilities - refresh tokens now handled by httpOnly cookies from backend
  // No client-side refresh token management needed

  // Clear all auth data from memory
  const clearAuthData = useCallback(() => {
    setUser(null);
    setProfile(null);
    setWorkspace(null);
  }, []);

  // Map better-auth organization to local Workspace type (best-effort)
  const mapOrganizationToWorkspace = useCallback((org: any): Workspace => ({
    id: org.id,
    company_name: org.name,
    company_type: 'team' as any,
    subscription_plan: 'basic' as any,
    domain: (org as any).domain,
    logo_url: org.logo,
    primary_color: '#3B82F6',
    address: undefined,
    phone: undefined,
    website: undefined,
    license_number: undefined,
    specialties: [],
    target_markets: [],
    status: 'active' as any,
    trial_ends_at: undefined,
    onboarding_completed: true,
    onboarding_step: 1,
    created_at: org.createdAt,
    updated_at: new Date().toISOString(),
  }), []);

  const hydrateWorkspaceFromSession = useCallback(async (activeOrganizationId?: string | null) => {
    try {
      const orgs = await betterAuthClient.listOrganizations();
      const active = (activeOrganizationId && orgs.find((o: any) => o.id === activeOrganizationId)) || orgs[0];
      if (active) {
        setWorkspace(mapOrganizationToWorkspace(active));
      } else {
        setWorkspace(null);
      }
    } catch (e) {
      setWorkspace(null);
    }
  }, [mapOrganizationToWorkspace]);

  // User data is now fetched by the API and returned in auth responses
  // No separate fetchUserData function needed

  // Check current session via better-auth (uses session cookie)
  const checkSession = useCallback(async () => {
    try {
      setError(null);
      console.log('AuthContext: Checking session using cookie authentication...');

      const sessionInfo = await betterAuthClient.getSession();
      console.log('AuthContext: Session check response:', {
        hasSession: !!sessionInfo.session,
        hasUser: !!sessionInfo.user,
        sessionId: sessionInfo.session?.id,
        userId: sessionInfo.user?.id
      });

      if (!sessionInfo.session || !sessionInfo.user) {
        throw new Error('No valid session found');
      }

      setUser(sessionInfo.user as any);
      setProfile(null);
      await hydrateWorkspaceFromSession(sessionInfo.session?.activeOrganizationId);

      console.log('AuthContext: Session check successful');
      return sessionInfo.user;

    } catch (error) {
      console.error('AuthContext: Session check failed:', error);

      // Clear auth state (backend should clear httpOnly session cookie)
      clearAuthData();
      setError(createAuthError(error, AuthErrorCode.SESSION_EXPIRED));
      throw error;
    }
  }, [createAuthError, clearAuthData, hydrateWorkspaceFromSession]);

  // Sign up via custom API endpoint
  const signUp = useCallback(async (data: SignUpData) => {
    try {
      setLoading(true);
      setError(null);

      const resp = await apiClient.signUpWithEmail(data);

      // Set auth state from response
      setUser(resp.user as any);
      setProfile(null);
      const activeOrgId = (resp as any)?.session?.activeOrganizationId ?? null;
      void hydrateWorkspaceFromSession(activeOrgId);

      // Verify cookie-based session is established
      try {
        await checkSession();
      } catch (e) {
        console.warn('Post-signup session check failed:', e);
      }

      console.log('Signup successful and user signed in');

    } catch (error) {
      console.error('Sign up error:', error);

      // Handle API errors
      if (isApiError(error)) {
        // Map API error status codes to our error codes
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 409) {
          errorCode = AuthErrorCode.EMAIL_ALREADY_EXISTS;
        } else if (error.status === 400) {
          errorCode = AuthErrorCode.WEAK_PASSWORD;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError]);



  // Sign in via custom API endpoint
  const signIn = useCallback(async (email: string, password: string) => {
    // Keep loading local to the form; do not block app rendering during org hydration
    let postAuthPhaseStarted = false;
    try {
      setError(null);

      // 1) Credential check (form shows its own spinner)
      const resp = await apiClient.signInWithEmail({ email, password, rememberMe: true });

      // 2) Post-auth hydration (non-blocking for app rendering)
      postAuthPhaseStarted = false;

      // Hydrate directly from response without extra session fetch
      setUser((resp as any).user as any);
      setProfile(null);
      const activeOrgId = (resp as any)?.session?.activeOrganizationId ?? null;
      // Fetch organizations/workspace in background so UI can render immediately
      void hydrateWorkspaceFromSession(activeOrgId);

      console.log('Sign in successful');

      // Verify cookie-based session is established
      try {
        await checkSession();
      } catch (e) {
        console.warn('Post-signin session check failed:', e);
      }

    } catch (error) {
      console.error('Sign in error:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.INVALID_CREDENTIALS; // Use closest available code for access denied
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      // No global loading during post-auth hydration
    }
  }, [createAuthError, hydrateWorkspaceFromSession, checkSession]);

  // Sign out function
  const signOut = useCallback(async () => {
    try {
      setLoading(true);
      setError(null);

      // Sign out via better-auth (clears cookie server-side)
      try {
        await betterAuthClient.signOut();
      } catch (apiError) {
        console.warn('Signout failed, continuing with cleanup:', apiError);
      }

      // Clear local state
      clearAuthData();

    } catch (error) {
      console.error('Sign out error:', error);
      setError(createAuthError(error));
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError, clearAuthData]);

  // Switch workspace via better-auth organization set-active
  const switchWorkspace = useCallback(async (workspaceId: string) => {
    try {
      setLoading(true);
      setError(null);

      // better-auth uses organizationId/slug
      await betterAuthClient.setActiveOrganization({ organizationId: workspaceId });

      const sessionInfo = await betterAuthClient.getSession();
      setProfile(null);
      await hydrateWorkspaceFromSession(sessionInfo.session?.activeOrganizationId);

      console.log('Workspace switch successful');

    } catch (error) {
      console.error('Workspace switch error:', error);

      // Handle API errors
      if (isApiError(error)) {
        let errorCode = AuthErrorCode.UNKNOWN_ERROR;
        if (error.status === 401) {
          errorCode = AuthErrorCode.UNKNOWN_ERROR;
        } else if (error.status === 403) {
          errorCode = AuthErrorCode.UNKNOWN_ERROR;
        }
        setError(createAuthError(error.message, errorCode));
      } else {
        setError(createAuthError(error));
      }
      throw error;
    } finally {
      setLoading(false);
    }
  }, [createAuthError]);


  // Initialize auth state and set up auth state listener
  useEffect(() => {
    let mounted = true;

    // Get initial session
    const initializeAuth = async () => {
      try {
        // Try to check the session (backend will check httpOnly cookie)
        try {
          await checkSession();
          console.log('Session restored via cookie');
        } catch (sessionError) {
          console.warn('Session check failed:', sessionError);
          // Backend will handle clearing invalid cookies
        }

        if (mounted) {
          setLoading(false);
        }
      } catch (error) {
        console.error('Auth initialization error:', error);
        if (mounted) {
          setError(createAuthError(error));
          setLoading(false);
        }
      }
    };

    initializeAuth();

    return () => {
      mounted = false;
    };
  }, [createAuthError, checkSession]);

  // Note: With cookie-based authentication, session management is handled by the server
  // No need for client-side token refresh intervals

  const value: AuthContextType = {
    user,
    profile,
    workspace,
    loading,
    error,
    signIn,
    signUp,
    signOut,
    switchWorkspace,
    clearError,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};